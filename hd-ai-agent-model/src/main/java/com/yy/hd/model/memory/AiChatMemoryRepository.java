package com.yy.hd.model.memory;

import lombok.AllArgsConstructor;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@AllArgsConstructor
public class AiChatMemoryRepository implements ChatMemoryRepository {

    private final JdbcTemplate jdbcTemplate;

    private final JdbcChatMemoryRepository jdbcChatMemoryRepository;

    private static final String INSERT_SQL = "INSERT INTO %s (conversation_id, content, type, `timestamp`) VALUES (?, ?, ?, ?)".formatted(AiChatHistory.TABLE);

    @Override
    public List<String> findConversationIds() {
        return jdbcChatMemoryRepository.findConversationIds();
    }

    @Override
    public List<Message> findByConversationId(String conversationId) {
        return jdbcChatMemoryRepository.findByConversationId(conversationId);
    }

    @Override
    public void saveAll(String conversationId, List<Message> messages) {
        jdbcChatMemoryRepository.saveAll(conversationId, messages);
        this.jdbcTemplate.batchUpdate(INSERT_SQL,
                new AddBatchPreparedStatement(conversationId, Collections.singletonList(messages.getLast())));
    }

    @Override
    public void deleteByConversationId(String conversationId) {
        jdbcChatMemoryRepository.deleteByConversationId(conversationId);
    }

    private record AddBatchPreparedStatement(String conversationId, List<Message> messages,
                                             AtomicLong instantSeq) implements BatchPreparedStatementSetter {

        private AddBatchPreparedStatement(String conversationId, List<Message> messages) {
            this(conversationId, messages, new AtomicLong(Instant.now().toEpochMilli()));
        }

        @Override
        public void setValues(PreparedStatement ps, int i) throws SQLException {
            var message = this.messages.get(i);

            ps.setString(1, this.conversationId);
            ps.setString(2, message.getText());
            ps.setString(3, message.getMessageType().name());
            ps.setTimestamp(4, new Timestamp(this.instantSeq.getAndIncrement()));
        }

        @Override
        public int getBatchSize() {
            return this.messages.size();
        }
    }
}
