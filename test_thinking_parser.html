<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>思维链解析测试</title>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #1a1a1a;
      color: #ffffff;
    }
    .test-input {
      width: 100%;
      height: 200px;
      margin: 10px 0;
      padding: 10px;
      background: #2a2a2a;
      color: #ffffff;
      border: 1px solid #444;
      border-radius: 5px;
    }
    .test-output {
      border: 1px solid #444;
      padding: 15px;
      margin: 10px 0;
      background: #2a2a2a;
      border-radius: 5px;
    }
    button {
      padding: 10px 20px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    button:hover {
      background: #2563eb;
    }
    .thinking-chain {
      background: #333;
      border: 1px solid #555;
      border-radius: 8px;
      margin: 1rem 0;
      overflow: hidden;
    }
    .thinking-chain-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      cursor: pointer;
      background: #444;
      border-bottom: 1px solid #555;
      user-select: none;
    }
    .thinking-chain-header:hover {
      background: #555;
    }
    .thinking-chain-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #b3b3b3;
    }
    .thinking-chain-icon {
      width: 16px;
      height: 16px;
      transition: transform 0.2s ease;
      color: #3b82f6;
    }
    .thinking-chain-header.expanded .thinking-chain-icon {
      transform: rotate(90deg);
    }
    .thinking-chain-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }
    .thinking-chain-content.expanded {
      max-height: 1000px;
    }
    .thinking-chain-inner {
      padding: 16px;
      color: #888;
      font-size: 14px;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .thinking-chain-badge {
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <h1>思维链解析功能测试</h1>
  
  <h3>输入测试内容：</h3>
  <textarea class="test-input" id="testInput" placeholder="输入包含 <think>...</think> 标签的内容">这是一个普通的回答。

<think>
让我思考一下这个问题...

首先，我需要理解用户的需求：
1. 用户想要什么？
2. 我应该如何回答？

经过分析，我认为应该这样回答...
</think>

基于我的分析，我的回答是：这是一个很好的问题！

<think>
再补充一些思考...
这个回答是否完整？
是否需要更多细节？
</think>

希望这个回答对您有帮助。</textarea>
  
  <button onclick="testParsing()">测试解析</button>
  
  <h3>解析结果：</h3>
  <div class="test-output" id="testOutput"></div>

  <script>
    // 配置 marked
    marked.setOptions({
      breaks: true,
      gfm: true
    });

    // HTML转义函数
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // 创建思维链HTML
    function createThinkingChainHtml(content, id) {
      const uniqueId = `thinking-chain-${Date.now()}-${id}`;
      return `
        <div class="thinking-chain" data-thinking-id="${uniqueId}">
          <div class="thinking-chain-header" onclick="toggleThinkingChain('${uniqueId}')">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">展开查看</span>
            </div>
          </div>
          <div class="thinking-chain-content" id="${uniqueId}-content">
            <div class="thinking-chain-inner">${escapeHtml(content)}</div>
          </div>
        </div>
      `;
    }

    // 处理思维链内容
    function processThinkingChain(content) {
      if (!content) return '';
      
      // 使用正则表达式匹配 <think>...</think> 标签
      const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
      let processedContent = content;
      let match;
      let thinkingChainId = 0;
      
      // 替换所有的 <think> 标签为思维链组件
      while ((match = thinkRegex.exec(content)) !== null) {
        const thinkingContent = match[1].trim();
        const thinkingChainHtml = createThinkingChainHtml(thinkingContent, thinkingChainId++);
        processedContent = processedContent.replace(match[0], thinkingChainHtml);
      }
      
      // 处理剩余的markdown内容
      const parts = processedContent.split(/(<div class="thinking-chain"[\s\S]*?<\/div>)/);
      let result = '';
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        if (part.includes('thinking-chain')) {
          // 这是思维链HTML，直接添加
          result += part;
        } else if (part.trim()) {
          // 这是普通内容，用marked处理
          result += marked.parse(part);
        }
      }
      
      return result;
    }

    // 切换思维链展开/折叠状态
    function toggleThinkingChain(id) {
      const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
      const content = document.getElementById(`${id}-content`);
      const badge = header.querySelector('.thinking-chain-badge');
      
      if (!header || !content || !badge) return;
      
      const isExpanded = header.classList.contains('expanded');
      
      if (isExpanded) {
        // 折叠
        header.classList.remove('expanded');
        content.classList.remove('expanded');
        badge.textContent = '展开查看';
      } else {
        // 展开
        header.classList.add('expanded');
        content.classList.add('expanded');
        badge.textContent = '收起';
      }
    }

    // 测试解析功能
    function testParsing() {
      const input = document.getElementById('testInput').value;
      const output = document.getElementById('testOutput');
      
      const processed = processThinkingChain(input);
      output.innerHTML = processed;
    }

    // 页面加载时运行一次测试
    window.onload = function() {
      testParsing();
    };
  </script>
</body>
</html>
