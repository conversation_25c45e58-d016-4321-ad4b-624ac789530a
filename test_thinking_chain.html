<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>思维链测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
    }

    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }

    .thinking-chain {
      background: var(--bg-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      margin: 1rem 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .thinking-chain-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.875rem 1.25rem;
      cursor: pointer;
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-secondary);
      transition: all 0.2s ease;
      user-select: none;
    }

    .thinking-chain-header:hover {
      background: var(--bg-hover);
    }

    .thinking-chain-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .thinking-chain-icon {
      width: 16px;
      height: 16px;
      transition: transform 0.2s ease;
      color: var(--accent-primary);
    }

    .thinking-chain-header.expanded .thinking-chain-icon {
      transform: rotate(90deg);
    }

    .thinking-chain-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .thinking-chain-content.expanded {
      max-height: 1000px;
    }

    .thinking-chain-inner {
      padding: 1.25rem;
      color: var(--text-tertiary);
      font-size: 0.875rem;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .thinking-chain-badge {
      background: var(--accent-light);
      color: var(--accent-primary);
      padding: 0.25rem 0.5rem;
      border-radius: var(--radius-sm);
      font-size: 0.75rem;
      font-weight: 500;
    }

    .test-content {
      background: var(--bg-tertiary);
      padding: 1.5rem;
      border-radius: var(--radius-lg);
      margin: 1rem 0;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>思维链功能测试</h1>
    
    <div class="test-content">
      <h3>测试内容：</h3>
      <p>这是一个普通的回答内容。</p>
      
      <div class="thinking-chain" data-thinking-id="test-1">
        <div class="thinking-chain-header" onclick="toggleThinkingChain('test-1')">
          <div class="thinking-chain-title">
            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>思维过程</span>
            <span class="thinking-chain-badge">展开查看</span>
          </div>
        </div>
        <div class="thinking-chain-content" id="test-1-content">
          <div class="thinking-chain-inner">这是一个思维链的内容示例。
用户问了一个关于AI的问题，我需要仔细思考如何回答。

首先，我需要理解问题的核心：
1. 用户想了解什么？
2. 我应该提供什么样的信息？
3. 如何组织我的回答？

经过思考，我认为应该从以下几个方面来回答：
- 基本概念的解释
- 实际应用的例子
- 未来发展的趋势

这样的回答结构会比较清晰和全面。</div>
        </div>
      </div>
      
      <p>这是思维链后面的普通内容。</p>
    </div>
  </div>

  <script>
    function toggleThinkingChain(id) {
      const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
      const content = document.getElementById(`${id}-content`);
      const badge = header.querySelector('.thinking-chain-badge');
      
      if (!header || !content || !badge) return;
      
      const isExpanded = header.classList.contains('expanded');
      
      if (isExpanded) {
        // 折叠
        header.classList.remove('expanded');
        content.classList.remove('expanded');
        badge.textContent = '展开查看';
      } else {
        // 展开
        header.classList.add('expanded');
        content.classList.add('expanded');
        badge.textContent = '收起';
      }
    }
  </script>
</body>
</html>
