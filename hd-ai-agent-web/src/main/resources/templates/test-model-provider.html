<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>模型提供商选择测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
    }

    .test-section {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .api-test {
      margin-bottom: 1rem;
    }

    .api-button {
      background: var(--accent-primary);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-right: 1rem;
      margin-bottom: 0.5rem;
    }

    .api-button:hover {
      background: var(--accent-hover);
    }

    .result-area {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      padding: 1rem;
      margin-top: 1rem;
      font-family: monospace;
      font-size: 0.875rem;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
    }

    .error {
      color: #ef4444;
    }

    .success {
      color: #10b981;
    }

    .info {
      color: #3b82f6;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">模型提供商选择功能测试</h1>
    
    <div class="test-section">
      <h2 class="section-title">API 接口测试</h2>
      
      <div class="api-test">
        <button class="api-button" onclick="testProvidersAPI()">测试 /providers 接口</button>
        <button class="api-button" onclick="testToolsAPI()">测试 /tools 接口</button>
        <button class="api-button" onclick="testDefaultSelection()">测试默认选择逻辑</button>
      </div>
      
      <div class="result-area" id="apiResult">
点击按钮测试API接口...
      </div>
    </div>
    
    <div class="test-section">
      <h2 class="section-title">功能说明</h2>
      <ul style="color: var(--text-secondary); line-height: 1.6;">
        <li>✅ 移除了联网搜索按钮及相关字段</li>
        <li>✅ 添加了大模型提供商选择按钮</li>
        <li>✅ 按钮点击后显示模型名称和描述</li>
        <li>✅ 按钮外观符合当前页面风格</li>
        <li>✅ 创建了 /providers API 接口</li>
        <li>✅ 支持多个提供商和模型配置</li>
        <li>✅ <strong>默认选中接口返回的第一个模型</strong></li>
      </ul>
    </div>

    <div class="test-section">
      <h2 class="section-title">默认选择逻辑</h2>
      <p style="color: var(--text-secondary); line-height: 1.6;">
        当用户首次访问页面或没有保存的模型选择时，系统会自动选择接口返回的第一个提供商的第一个模型作为默认选择。
        这确保了用户始终有一个可用的模型进行对话。
      </p>
    </div>
  </div>

  <script>
    async function testProvidersAPI() {
      const resultArea = document.getElementById('apiResult');
      resultArea.textContent = '正在测试 /providers 接口...';
      
      try {
        const response = await fetch('/providers');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        resultArea.innerHTML = `<span class="success">✅ /providers 接口测试成功</span>\n\n${JSON.stringify(data, null, 2)}`;
      } catch (error) {
        resultArea.innerHTML = `<span class="error">❌ /providers 接口测试失败</span>\n\n错误信息: ${error.message}`;
      }
    }
    
    async function testToolsAPI() {
      const resultArea = document.getElementById('apiResult');
      resultArea.textContent = '正在测试 /tools 接口...';
      
      try {
        const response = await fetch('/tools');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        resultArea.innerHTML = `<span class="success">✅ /tools 接口测试成功</span>\n\n${JSON.stringify(data, null, 2)}`;
      } catch (error) {
        resultArea.innerHTML = `<span class="error">❌ /tools 接口测试失败</span>\n\n错误信息: ${error.message}`;
      }
    }

    async function testDefaultSelection() {
      const resultArea = document.getElementById('apiResult');
      resultArea.innerHTML = '<span class="info">🔍 测试默认选择逻辑...</span>';
      
      try {
        // 清除本地存储
        localStorage.removeItem('selectedModelProvider');
        localStorage.removeItem('selectedModel');
        
        // 获取提供商数据
        const response = await fetch('/providers');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const providers = await response.json();
        
        if (providers.length === 0) {
          resultArea.innerHTML = '<span class="error">❌ 没有可用的提供商</span>';
          return;
        }
        
        const firstProvider = providers[0];
        if (!firstProvider.models || firstProvider.models.length === 0) {
          resultArea.innerHTML = '<span class="error">❌ 第一个提供商没有可用的模型</span>';
          return;
        }
        
        const firstModel = firstProvider.models[0];
        
        let result = `<span class="success">✅ 默认选择逻辑测试成功</span>\n\n`;
        result += `<span class="info">默认选择结果：</span>\n`;
        result += `提供商: ${firstProvider.provider}\n`;
        result += `模型名称: ${firstModel.modelName}\n`;
        result += `显示名称: ${firstModel.displayName || firstModel.modelName}\n`;
        result += `描述: ${firstModel.description || '暂无描述'}\n\n`;
        result += `<span class="info">完整提供商数据：</span>\n`;
        result += JSON.stringify(providers, null, 2);
        
        resultArea.innerHTML = result;
        
      } catch (error) {
        resultArea.innerHTML = `<span class="error">❌ 默认选择逻辑测试失败</span>\n\n错误信息: ${error.message}`;
      }
    }
  </script>
</body>
</html>
